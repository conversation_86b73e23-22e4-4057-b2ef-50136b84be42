import React, { createContext, useContext, useState, useEffect } from 'react';
import { Car } from '../types';
import axios from 'axios';
import { apiClient, API_ENDPOINTS } from '../config/api';

interface CarContextType {
  cars: Car[];
  filteredCars: Car[];
  activeCars: Car[]; // Only active cars for homepage
  selectedCar: Car | null;
  isLoading: boolean;
  error: string | null;
  fetchCars: () => Promise<void>;
  getCarById: (id: string) => Promise<Car | null>;
  setSelectedCar: (car: Car | null) => void;
  addCar: (car: Omit<Car, 'id' | 'createdAt'>, imageFiles?: File[]) => Promise<Car>;
  updateCar: (id: string, car: Partial<Car>) => Promise<Car>;
  deleteCar: (id: string) => Promise<void>;
  filterCars: (query: string) => void;
}

const CarContext = createContext<CarContextType | undefined>(undefined);

export const useCars = () => {
  const context = useContext(CarContext);
  if (context === undefined) {
    throw new Error('useCars must be used within a CarProvider');
  }
  return context;
};

export const CarProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [cars, setCars] = useState<Car[]>([]);
  const [filteredCars, setFilteredCars] = useState<Car[]>([]);
  const [activeCars, setActiveCars] = useState<Car[]>([]); // Only active cars
  const [selectedCar, setSelectedCar] = useState<Car | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchCars = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Make the API request
      const response = await apiClient.get(API_ENDPOINTS.CARS);

      // If successful, the API will return the cars data
      if (response.data && Array.isArray(response.data.cars)) {
        // Transform the API response to match our Car type
        const fetchedCars: Car[] = response.data.cars.map((car: any) => ({
          id: car.id.toString(),
          ownerId: car.owner_id.toString(),
          make: car.make,
          model: car.model,
          year: car.year,
          images: car.images || [],
          description: car.description,
          features: car.features || [],
          location: car.location,
          pricePerHour: car.price_per_hour,
          availabilityNotes: car.availability_notes || '',
          isActive: car.is_active,
          createdAt: car.created_at,
        }));

        const activeCarsList = fetchedCars.filter(car => car.isActive);
        setCars(fetchedCars);
        setFilteredCars(activeCarsList); // Initialize with active cars only
        setActiveCars(activeCarsList);
      } else {
        // API didn't return expected format
        console.warn('API did not return expected data format');
        setCars([]);
        setFilteredCars([]);
        setActiveCars([]);
        setError('API did not return expected data format');
      }
    } catch (err) {
      console.error('Error fetching cars from API:', err);
      // Clear cars on error
      setCars([]);
      setFilteredCars([]);
      setActiveCars([]);
      setError('Failed to fetch cars from server');
    } finally {
      setIsLoading(false);
    }
  };

  const getCarById = async (id: string): Promise<Car | null> => {
    setIsLoading(true);
    setError(null);

    try {
      // First check if we already have the car in state
      const cachedCar = cars.find(c => c.id === id);
      if (cachedCar) {
        setSelectedCar(cachedCar);
        return cachedCar;
      }

      // If not found in state, fetch from API
      // Make the API request
      const response = await apiClient.get(`${API_ENDPOINTS.CARS}/${id}`);

      // If successful, the API will return the car data
      if (response.data && response.data.car) {
        // Transform the API response to match our Car type
        const car: Car = {
          id: response.data.car.id.toString(),
          ownerId: response.data.car.owner_id.toString(),
          make: response.data.car.make,
          model: response.data.car.model,
          year: response.data.car.year,
          images: response.data.car.images || [],
          description: response.data.car.description,
          features: response.data.car.features || [],
          location: response.data.car.location,
          pricePerHour: response.data.car.price_per_hour,
          availabilityNotes: response.data.car.availability_notes || '',
          isActive: response.data.car.is_active,
          createdAt: response.data.car.created_at,
        };

        setSelectedCar(car);
        return car;
      } else {
        setError('Car not found');
        return null;
      }
    } catch (err) {
      setError('Failed to fetch car details');
      console.error(err);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  const addCar = async (carData: Omit<Car, 'id' | 'createdAt'>, imageFiles?: File[]): Promise<Car> => {
    setIsLoading(true);
    setError(null);

    try {
      // Get the auth token (this is handled by apiClient interceptor, but we check for user feedback)
      const token = localStorage.getItem('auth_token');

      if (!token) {
        throw new Error('Authentication token not found');
      }

      // Create FormData to handle file uploads
      const formData = new FormData();

      // Add car data to FormData
      formData.append('make', carData.make);
      formData.append('model', carData.model);
      formData.append('year', carData.year.toString());
      formData.append('description', carData.description);
      formData.append('location', carData.location);
      formData.append('price_per_hour', carData.pricePerHour.toString());
      formData.append('availability_notes', carData.availabilityNotes || '');
      formData.append('is_active', carData.isActive ? '1' : '0');

      // Add features as JSON
      formData.append('features', JSON.stringify(carData.features));

      // Add image files if provided
      if (imageFiles && imageFiles.length > 0) {
        imageFiles.forEach((file, index) => {
          formData.append(`images[${index}]`, file);
        });
      } else if (carData.images && carData.images.length > 0) {
        // If no files but URLs are provided (for backward compatibility)
        formData.append('image_urls', JSON.stringify(carData.images));
      }

      // Get auth token for explicit header
      const authToken = localStorage.getItem('auth_token');

      // Make the API request with explicit auth header for FormData
      const response = await apiClient.post(API_ENDPOINTS.CARS, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          'Authorization': `Bearer ${authToken}`,
        }
      });

      // If successful, the API will return the new car data
      if (response.data && response.data.car) {
        // Transform the API response to match our Car type
        const newCar: Car = {
          id: response.data.car.id.toString(),
          ownerId: response.data.car.owner_id.toString(),
          make: response.data.car.make,
          model: response.data.car.model,
          year: response.data.car.year,
          images: response.data.car.images || [],
          description: response.data.car.description,
          features: response.data.car.features || [],
          location: response.data.car.location,
          pricePerHour: response.data.car.price_per_hour,
          availabilityNotes: response.data.car.availability_notes || '',
          isActive: response.data.car.is_active,
          createdAt: response.data.car.created_at,
        };

        setCars(prevCars => [...prevCars, newCar]);

        // Only add to filtered cars if the car is active
        if (newCar.isActive) {
          setFilteredCars(prevCars => [...prevCars, newCar]);
          setActiveCars(prevCars => [...prevCars, newCar]);
        }
        return newCar;
      } else {
        throw new Error('Failed to add car: Invalid response from server');
      }
    } catch (err) {
      // Handle API errors
      console.error('Add car error:', err);
      if (axios.isAxiosError(err) && err.response) {
        console.error('Response data:', err.response.data);
        console.error('Response status:', err.response.status);
        if (err.response.data && err.response.data.errors) {
          const errorMessages = Object.values(err.response.data.errors)
            .flat()
            .join(', ');
          setError(errorMessages);
        } else if (err.response.data && err.response.data.message) {
          setError(err.response.data.message);
        } else {
          setError(`Failed to add car: ${err.response.statusText}`);
        }
      } else {
        setError(err instanceof Error ? err.message : 'Failed to add car');
      }
      console.error(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const updateCar = async (id: string, carData: Partial<Car>): Promise<Car> => {
    setIsLoading(true);
    setError(null);

    try {
      // Get the auth token (this is handled by apiClient interceptor, but we check for user feedback)
      const token = localStorage.getItem('auth_token');

      if (!token) {
        throw new Error('Authentication token not found');
      }

      // Transform carData to match API expectations
      const apiCarData: any = {};

      if (carData.make) apiCarData.make = carData.make;
      if (carData.model) apiCarData.model = carData.model;
      if (carData.year) apiCarData.year = carData.year;
      if (carData.description) apiCarData.description = carData.description;
      if (carData.location) apiCarData.location = carData.location;
      if (carData.pricePerHour) apiCarData.price_per_hour = carData.pricePerHour;
      if (carData.availabilityNotes !== undefined) apiCarData.availability_notes = carData.availabilityNotes;
      if (carData.isActive !== undefined) apiCarData.is_active = carData.isActive ? 1 : 0;
      if (carData.features) apiCarData.features = JSON.stringify(carData.features);

      // Make the API request (apiClient will automatically add auth header)
      const response = await apiClient.put(`${API_ENDPOINTS.CARS}/${id}`, apiCarData);

      // If successful, the API will return the updated car data
      if (response.data && response.data.car) {
        // Transform the API response to match our Car type
        const updatedCar: Car = {
          id: response.data.car.id.toString(),
          ownerId: response.data.car.owner_id.toString(),
          make: response.data.car.make,
          model: response.data.car.model,
          year: response.data.car.year,
          images: response.data.car.images || [],
          description: response.data.car.description,
          features: response.data.car.features || [],
          location: response.data.car.location,
          pricePerHour: response.data.car.price_per_hour,
          availabilityNotes: response.data.car.availability_notes || '',
          isActive: response.data.car.is_active,
          createdAt: response.data.car.created_at,
        };

        // Update the cars state
        setCars(prevCars => {
          const updatedCars = [...prevCars];
          const carIndex = updatedCars.findIndex(c => c.id === id);
          if (carIndex !== -1) {
            updatedCars[carIndex] = updatedCar;
          }
          return updatedCars;
        });

        // Update the filtered cars state (only active cars for public)
        setFilteredCars(prevCars => {
          const updatedCars = [...prevCars];
          const carIndex = updatedCars.findIndex(c => c.id === id);

          if (updatedCar.isActive) {
            // Car is now active
            if (carIndex === -1) {
              // Add to filtered cars if not already there
              updatedCars.push(updatedCar);
            } else {
              // Update existing car in filtered list
              updatedCars[carIndex] = updatedCar;
            }
          } else {
            // Car is now inactive, remove from filtered cars
            if (carIndex !== -1) {
              updatedCars.splice(carIndex, 1);
            }
          }
          return updatedCars;
        });

        // Update the active cars state
        setActiveCars(prevCars => {
          const updatedCars = [...prevCars];
          const carIndex = updatedCars.findIndex(c => c.id === id);

          if (updatedCar.isActive) {
            // Car is now active
            if (carIndex === -1) {
              // Add to active cars if not already there
              updatedCars.push(updatedCar);
            } else {
              // Update existing active car
              updatedCars[carIndex] = updatedCar;
            }
          } else {
            // Car is now inactive, remove from active cars
            if (carIndex !== -1) {
              updatedCars.splice(carIndex, 1);
            }
          }
          return updatedCars;
        });

        // Update the selected car if it's the one being updated
        if (selectedCar?.id === id) {
          setSelectedCar(updatedCar);
        }

        return updatedCar;
      } else {
        throw new Error('Failed to update car: Invalid response from server');
      }
    } catch (err) {
      // Handle API errors
      if (axios.isAxiosError(err) && err.response) {
        if (err.response.data && err.response.data.errors) {
          const errorMessages = Object.values(err.response.data.errors)
            .flat()
            .join(', ');
          setError(errorMessages);
        } else if (err.response.data && err.response.data.message) {
          setError(err.response.data.message);
        } else {
          setError(`Failed to update car: ${err.response.statusText}`);
        }
      } else {
        setError(err instanceof Error ? err.message : 'Failed to update car');
      }
      console.error(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const deleteCar = async (id: string): Promise<void> => {
    setIsLoading(true);
    setError(null);

    try {
      // Get the auth token (this is handled by apiClient interceptor, but we check for user feedback)
      const token = localStorage.getItem('auth_token');

      if (!token) {
        throw new Error('Authentication token not found');
      }

      // Make the API request (apiClient will automatically add auth header)
      await apiClient.delete(`${API_ENDPOINTS.CARS}/${id}`);

      // Update the local state
      setCars(prevCars => prevCars.filter(car => car.id !== id));
      setFilteredCars(prevCars => prevCars.filter(car => car.id !== id));
      setActiveCars(prevCars => prevCars.filter(car => car.id !== id));

      if (selectedCar?.id === id) {
        setSelectedCar(null);
      }
    } catch (err) {
      // Handle API errors
      if (axios.isAxiosError(err) && err.response) {
        if (err.response.data && err.response.data.message) {
          setError(err.response.data.message);
        } else {
          setError(`Failed to delete car: ${err.response.statusText}`);
        }
      } else {
        setError(err instanceof Error ? err.message : 'Failed to delete car');
      }
      console.error(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const filterCars = (query: string) => {
    // Only filter among active cars for public search
    const carsToFilter = activeCars;

    if (!query.trim()) {
      setFilteredCars(carsToFilter);
      return;
    }

    const lowerQuery = query.toLowerCase();
    const filtered = carsToFilter.filter(car =>
      car.make.toLowerCase().includes(lowerQuery) ||
      car.model.toLowerCase().includes(lowerQuery) ||
      car.location.toLowerCase().includes(lowerQuery) ||
      car.description.toLowerCase().includes(lowerQuery)
    );

    setFilteredCars(filtered);
  };

  useEffect(() => {
    fetchCars();
  }, []);

  const value = {
    cars,
    filteredCars,
    activeCars,
    selectedCar,
    isLoading,
    error,
    fetchCars,
    getCarById,
    setSelectedCar,
    addCar,
    updateCar,
    deleteCar,
    filterCars,
  };

  return <CarContext.Provider value={value}>{children}</CarContext.Provider>;
};