import React, { createContext, useContext, useState, useEffect } from 'react';
import { Car } from '../types';
import { mockCars } from '../data/mockData';

interface CarContextType {
  cars: Car[];
  filteredCars: Car[];
  selectedCar: Car | null;
  isLoading: boolean;
  error: string | null;
  fetchCars: () => Promise<void>;
  getCarById: (id: string) => Promise<Car | null>;
  setSelectedCar: (car: Car | null) => void;
  addCar: (car: Omit<Car, 'id' | 'createdAt'>) => Promise<Car>;
  updateCar: (id: string, car: Partial<Car>) => Promise<Car>;
  deleteCar: (id: string) => Promise<void>;
  filterCars: (query: string) => void;
}

const CarContext = createContext<CarContextType | undefined>(undefined);

export const useCars = () => {
  const context = useContext(CarContext);
  if (context === undefined) {
    throw new Error('useCars must be used within a CarProvider');
  }
  return context;
};

export const CarProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [cars, setCars] = useState<Car[]>([]);
  const [filteredCars, setFilteredCars] = useState<Car[]>([]);
  const [selectedCar, setSelectedCar] = useState<Car | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchCars = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Simulate API call with mock data
      // In a real app, this would be an API request
      setCars(mockCars);
      setFilteredCars(mockCars);
    } catch (err) {
      setError('Failed to fetch cars');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  const getCarById = async (id: string): Promise<Car | null> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const car = cars.find(c => c.id === id) || null;
      setSelectedCar(car);
      return car;
    } catch (err) {
      setError('Failed to fetch car details');
      console.error(err);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  const addCar = async (carData: Omit<Car, 'id' | 'createdAt'>): Promise<Car> => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Create new car
      const newCar: Car = {
        ...carData,
        id: `car-${Date.now()}`,
        createdAt: new Date().toISOString(),
      };
      
      setCars(prevCars => [...prevCars, newCar]);
      setFilteredCars(prevCars => [...prevCars, newCar]);
      return newCar;
    } catch (err) {
      setError('Failed to add car');
      console.error(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const updateCar = async (id: string, carData: Partial<Car>): Promise<Car> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const carIndex = cars.findIndex(c => c.id === id);
      if (carIndex === -1) {
        throw new Error('Car not found');
      }
      
      const updatedCar = { ...cars[carIndex], ...carData };
      const updatedCars = [...cars];
      updatedCars[carIndex] = updatedCar;
      
      setCars(updatedCars);
      setFilteredCars(updatedCars);
      
      if (selectedCar?.id === id) {
        setSelectedCar(updatedCar);
      }
      
      return updatedCar;
    } catch (err) {
      setError('Failed to update car');
      console.error(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const deleteCar = async (id: string): Promise<void> => {
    setIsLoading(true);
    setError(null);
    
    try {
      setCars(prevCars => prevCars.filter(car => car.id !== id));
      setFilteredCars(prevCars => prevCars.filter(car => car.id !== id));
      
      if (selectedCar?.id === id) {
        setSelectedCar(null);
      }
    } catch (err) {
      setError('Failed to delete car');
      console.error(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const filterCars = (query: string) => {
    if (!query.trim()) {
      setFilteredCars(cars);
      return;
    }
    
    const lowerQuery = query.toLowerCase();
    const filtered = cars.filter(car => 
      car.make.toLowerCase().includes(lowerQuery) ||
      car.model.toLowerCase().includes(lowerQuery) ||
      car.location.toLowerCase().includes(lowerQuery) ||
      car.description.toLowerCase().includes(lowerQuery)
    );
    
    setFilteredCars(filtered);
  };

  useEffect(() => {
    fetchCars();
  }, []);

  const value = {
    cars,
    filteredCars,
    selectedCar,
    isLoading,
    error,
    fetchCars,
    getCarById,
    setSelectedCar,
    addCar,
    updateCar,
    deleteCar,
    filterCars,
  };

  return <CarContext.Provider value={value}>{children}</CarContext.Provider>;
};