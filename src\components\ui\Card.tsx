import React from 'react';

interface CardProps {
  className?: string;
  children: React.ReactNode;
  onClick?: () => void;
  hover?: boolean;
}

const Card: React.FC<CardProps> = ({
  className = '',
  children,
  onClick,
  hover = true,
}) => {
  const baseClasses = 'bg-white rounded-lg shadow-md overflow-hidden transition-all duration-200';
  const hoverClasses = hover ? 'hover:shadow-lg transform hover:-translate-y-1' : '';
  const clickableClasses = onClick ? 'cursor-pointer' : '';
  
  const classes = `${baseClasses} ${hoverClasses} ${clickableClasses} ${className}`;
  
  return (
    <div className={classes} onClick={onClick}>
      {children}
    </div>
  );
};

export interface CardImageProps {
  src: string;
  alt: string;
  className?: string;
}

export const CardImage: React.FC<CardImageProps> = ({ src, alt, className = '' }) => (
  <div className={`aspect-video w-full overflow-hidden ${className}`}>
    <img
      src={src}
      alt={alt}
      className="w-full h-full object-cover"
    />
  </div>
);

export interface CardContentProps {
  className?: string;
  children: React.ReactNode;
}

export const CardContent: React.FC<CardContentProps> = ({ className = '', children }) => (
  <div className={`p-4 ${className}`}>
    {children}
  </div>
);

export interface CardTitleProps {
  className?: string;
  children: React.ReactNode;
}

export const CardTitle: React.FC<CardTitleProps> = ({ className = '', children }) => (
  <h3 className={`font-bold text-lg mb-2 ${className}`}>
    {children}
  </h3>
);

export interface CardFooterProps {
  className?: string;
  children: React.ReactNode;
}

export const CardFooter: React.FC<CardFooterProps> = ({ className = '', children }) => (
  <div className={`px-4 py-3 bg-gray-50 border-t border-gray-100 ${className}`}>
    {children}
  </div>
);

export default Object.assign(Card, {
  Image: CardImage,
  Content: CardContent,
  Title: CardTitle,
  Footer: CardFooter,
});