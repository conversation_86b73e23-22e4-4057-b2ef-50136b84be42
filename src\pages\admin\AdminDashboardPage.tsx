import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  Users, Car, UserRound, Calendar, CheckCircle, XCircle, 
  AlertTriangle, BarChart2, DollarSign, Search, Filter 
} from 'lucide-react';
import MainLayout from '../../components/layout/MainLayout';
import Button from '../../components/ui/Button';
import { useCars } from '../../context/CarContext';
import { useDrivers } from '../../context/DriverContext';
import { useBookings } from '../../context/BookingContext';
import { useAuth } from '../../context/AuthContext';
import { mockUsers } from '../../data/mockData';
import Spinner from '../../components/ui/Spinner';
import { User, Car as CarType, Driver, Booking } from '../../types';

// Admin role check - in a real app, this would be part of the User type
const isAdmin = (user: User | null) => {
  // For demo purposes, consider user-1 as admin
  return user?.id === 'user-1';
};

const AdminDashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { cars, updateCar } = useCars();
  const { drivers, updateDriver } = useDrivers();
  const { bookings, updateBookingStatus } = useBookings();
  
  const [activeTab, setActiveTab] = useState<'overview' | 'users' | 'cars' | 'drivers' | 'bookings'>('overview');
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  
  // Stats for overview
  const totalUsers = mockUsers.length;
  const totalCars = cars.length;
  const totalDrivers = drivers.length;
  const totalBookings = bookings.length;
  const pendingVerifications = mockUsers.filter(u => 
    u.licenseVerificationStatus === 'pending'
  ).length;
  
  // Filtered lists based on search and filters
  const filteredUsers = mockUsers.filter(user => 
    user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.email.toLowerCase().includes(searchQuery.toLowerCase())
  );
  
  const filteredCars = cars.filter(car => 
    (car.make.toLowerCase().includes(searchQuery.toLowerCase()) ||
     car.model.toLowerCase().includes(searchQuery.toLowerCase()) ||
     car.location.toLowerCase().includes(searchQuery.toLowerCase())) &&
    (statusFilter === 'all' || 
     (statusFilter === 'active' && car.isActive) ||
     (statusFilter === 'inactive' && !car.isActive))
  );
  
  const filteredDrivers = drivers.filter(driver => 
    (driver.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
     driver.location.toLowerCase().includes(searchQuery.toLowerCase())) &&
    (statusFilter === 'all' || 
     (statusFilter === 'active' && driver.isAvailable) ||
     (statusFilter === 'inactive' && !driver.isAvailable) ||
     (statusFilter === 'pending' && driver.licenseVerificationStatus === 'pending'))
  );
  
  const filteredBookings = bookings.filter(booking => 
    (statusFilter === 'all' || booking.status === statusFilter)
  );
  
  const handleVerifyLicense = async (userId: string, type: 'user' | 'driver', id: string, status: 'verified' | 'rejected') => {
    setIsLoading(true);
    try {
      if (type === 'driver') {
        await updateDriver(id, { licenseVerificationStatus: status });
      }
      // In a real app, you would also update the user's license status
    } catch (error) {
      console.error('Failed to update verification status:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleUpdateBookingStatus = async (bookingId: string, status: Booking['status']) => {
    setIsLoading(true);
    try {
      await updateBookingStatus(bookingId, status);
    } catch (error) {
      console.error('Failed to update booking status:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  if (!user || !isAdmin(user)) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="bg-error-50 border border-error-200 rounded-lg p-4 text-center">
            <h2 className="text-xl font-medium text-error-800 mb-2">Access Denied</h2>
            <p className="text-error-600">
              You must be logged in as an administrator to access this page.
            </p>
            <Button 
              className="mt-4"
              onClick={() => navigate('/login')}
            >
              Log In
            </Button>
          </div>
        </div>
      </MainLayout>
    );
  }
  
  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Admin Dashboard</h1>
            <p className="text-gray-600">
              Manage users, cars, drivers, and bookings.
            </p>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-md overflow-hidden mb-8">
          <div className="flex flex-wrap border-b border-gray-200">
            <button
              className={`px-6 py-3 font-medium text-sm focus:outline-none ${
                activeTab === 'overview'
                  ? 'text-primary-600 border-b-2 border-primary-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
              onClick={() => setActiveTab('overview')}
            >
              <BarChart2 size={16} className="inline-block mr-1" />
              Overview
            </button>
            <button
              className={`px-6 py-3 font-medium text-sm focus:outline-none ${
                activeTab === 'users'
                  ? 'text-primary-600 border-b-2 border-primary-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
              onClick={() => setActiveTab('users')}
            >
              <Users size={16} className="inline-block mr-1" />
              Users
            </button>
            <button
              className={`px-6 py-3 font-medium text-sm focus:outline-none ${
                activeTab === 'cars'
                  ? 'text-primary-600 border-b-2 border-primary-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
              onClick={() => setActiveTab('cars')}
            >
              <Car size={16} className="inline-block mr-1" />
              Cars
            </button>
            <button
              className={`px-6 py-3 font-medium text-sm focus:outline-none ${
                activeTab === 'drivers'
                  ? 'text-primary-600 border-b-2 border-primary-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
              onClick={() => setActiveTab('drivers')}
            >
              <UserRound size={16} className="inline-block mr-1" />
              Drivers
            </button>
            <button
              className={`px-6 py-3 font-medium text-sm focus:outline-none ${
                activeTab === 'bookings'
                  ? 'text-primary-600 border-b-2 border-primary-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
              onClick={() => setActiveTab('bookings')}
            >
              <Calendar size={16} className="inline-block mr-1" />
              Bookings
            </button>
          </div>
          
          {/* Overview Tab */}
          {activeTab === 'overview' && (
            <div className="p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-6">Platform Overview</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div className="bg-blue-50 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-blue-800">Total Users</h3>
                    <Users size={24} className="text-blue-500" />
                  </div>
                  <p className="text-3xl font-bold text-blue-600">{totalUsers}</p>
                  <div className="mt-2 text-sm text-blue-600">
                    <span className="font-medium">{mockUsers.filter(u => u.role === 'client').length}</span> Clients, 
                    <span className="font-medium ml-1">{mockUsers.filter(u => u.role === 'owner').length}</span> Owners,
                    <span className="font-medium ml-1">{mockUsers.filter(u => u.role === 'driver').length}</span> Drivers
                  </div>
                </div>
                
                <div className="bg-green-50 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-green-800">Total Cars</h3>
                    <Car size={24} className="text-green-500" />
                  </div>
                  <p className="text-3xl font-bold text-green-600">{totalCars}</p>
                  <div className="mt-2 text-sm text-green-600">
                    <span className="font-medium">{cars.filter(c => c.isActive).length}</span> Active, 
                    <span className="font-medium ml-1">{cars.filter(c => !c.isActive).length}</span> Inactive
                  </div>
                </div>
                
                <div className="bg-purple-50 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-purple-800">Total Drivers</h3>
                    <UserRound size={24} className="text-purple-500" />
                  </div>
                  <p className="text-3xl font-bold text-purple-600">{totalDrivers}</p>
                  <div className="mt-2 text-sm text-purple-600">
                    <span className="font-medium">{drivers.filter(d => d.isAvailable).length}</span> Available, 
                    <span className="font-medium ml-1">{drivers.filter(d => !d.isAvailable).length}</span> Unavailable
                  </div>
                </div>
                
                <div className="bg-yellow-50 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-yellow-800">Total Bookings</h3>
                    <Calendar size={24} className="text-yellow-500" />
                  </div>
                  <p className="text-3xl font-bold text-yellow-600">{totalBookings}</p>
                  <div className="mt-2 text-sm text-yellow-600">
                    <span className="font-medium">{bookings.filter(b => b.status === 'completed').length}</span> Completed, 
                    <span className="font-medium ml-1">{bookings.filter(b => b.status === 'pending').length}</span> Pending
                  </div>
                </div>
              </div>
              
              <div className="bg-orange-50 rounded-lg p-6 mb-8">
                <div className="flex items-center mb-4">
                  <AlertTriangle size={24} className="text-orange-500 mr-2" />
                  <h3 className="text-lg font-medium text-orange-800">Pending Verifications</h3>
                </div>
                <p className="text-3xl font-bold text-orange-600 mb-2">{pendingVerifications}</p>
                <p className="text-sm text-orange-700">
                  {pendingVerifications} users are waiting for license verification. Review these to maintain platform safety.
                </p>
                <Button 
                  variant="outline" 
                  className="mt-4 border-orange-500 text-orange-700 hover:bg-orange-100"
                  onClick={() => setActiveTab('users')}
                >
                  Review Verifications
                </Button>
              </div>
              
              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-800 mb-4">Recent Activity</h3>
                <div className="space-y-4">
                  {bookings.slice(0, 5).map((booking, index) => (
                    <div key={index} className="flex items-start">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                        booking.status === 'completed' ? 'bg-success-100' :
                        booking.status === 'pending' ? 'bg-yellow-100' :
                        booking.status === 'confirmed' ? 'bg-blue-100' : 'bg-error-100'
                      }`}>
                        <Calendar size={16} className={`${
                          booking.status === 'completed' ? 'text-success-600' :
                          booking.status === 'pending' ? 'text-yellow-600' :
                          booking.status === 'confirmed' ? 'text-blue-600' : 'text-error-600'
                        }`} />
                      </div>
                      <div className="ml-3">
                        <p className="text-sm font-medium text-gray-900">
                          New {booking.itemType} booking ({booking.status})
                        </p>
                        <p className="text-xs text-gray-500">
                          {new Date(booking.createdAt).toLocaleString()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
          
          {/* Users Tab */}
          {activeTab === 'users' && (
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-bold text-gray-900">Users Management</h2>
                
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search size={16} className="text-gray-400" />
                  </div>
                  <input
                    type="text"
                    placeholder="Search users..."
                    className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>
              
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        User
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Role
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Joined
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        License Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredUsers.map((user) => (
                      <tr key={user.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                              {user.licenseImageUrl ? (
                                <img
                                  src={user.licenseImageUrl}
                                  alt={user.name}
                                  className="h-10 w-10 rounded-full object-cover"
                                />
                              ) : (
                                <UserRound size={20} className="text-gray-500" />
                              )}
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">
                                {user.name}
                              </div>
                              <div className="text-sm text-gray-500">
                                {user.email}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            user.role === 'client'
                              ? 'bg-blue-100 text-blue-800'
                              : user.role === 'owner'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-purple-100 text-purple-800'
                          }`}>
                            {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(user.createdAt).toLocaleDateString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {user.licenseVerificationStatus ? (
                            <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                              user.licenseVerificationStatus === 'verified'
                                ? 'bg-success-100 text-success-800'
                                : user.licenseVerificationStatus === 'pending'
                                ? 'bg-yellow-100 text-yellow-800'
                                : 'bg-error-100 text-error-800'
                            }`}>
                              {user.licenseVerificationStatus.charAt(0).toUpperCase() + 
                               user.licenseVerificationStatus.slice(1)}
                            </span>
                          ) : (
                            <span className="text-gray-500 text-sm">N/A</span>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          {user.licenseVerificationStatus === 'pending' && (
                            <div className="flex space-x-2">
                              <button
                                className="text-success-600 hover:text-success-800"
                                onClick={() => handleVerifyLicense(user.id, 'user', user.id, 'verified')}
                              >
                                <CheckCircle size={18} />
                              </button>
                              <button
                                className="text-error-600 hover:text-error-800"
                                onClick={() => handleVerifyLicense(user.id, 'user', user.id, 'rejected')}
                              >
                                <XCircle size={18} />
                              </button>
                            </div>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
          
          {/* Other tabs would be implemented similarly */}
          {(activeTab === 'cars' || activeTab === 'drivers' || activeTab === 'bookings') && (
            <div className="p-6 text-center">
              <h2 className="text-xl font-bold text-gray-900 mb-4">
                {activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} Management
              </h2>
              <p className="text-gray-600 mb-6">
                This section would contain detailed management tools for {activeTab}.
              </p>
              <div className="bg-gray-50 p-8 rounded-lg">
                <div className="text-gray-400 mb-4">
                  {activeTab === 'cars' ? <Car size={48} className="mx-auto" /> : 
                   activeTab === 'drivers' ? <UserRound size={48} className="mx-auto" /> :
                   <Calendar size={48} className="mx-auto" />}
                </div>
                <p className="text-gray-700">
                  In a complete implementation, this tab would show:
                </p>
                <ul className="text-gray-600 mt-2 space-y-1">
                  {activeTab === 'cars' && (
                    <>
                      <li>• List of all cars with filtering and search</li>
                      <li>• Car approval/rejection functionality</li>
                      <li>• Car details and owner information</li>
                    </>
                  )}
                  {activeTab === 'drivers' && (
                    <>
                      <li>• List of all drivers with filtering and search</li>
                      <li>• Driver verification management</li>
                      <li>• Driver performance metrics</li>
                    </>
                  )}
                  {activeTab === 'bookings' && (
                    <>
                      <li>• All platform bookings with filtering options</li>
                      <li>• Booking status management</li>
                      <li>• Dispute resolution tools</li>
                    </>
                  )}
                </ul>
              </div>
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
};

export default AdminDashboardPage;
