import React, { createContext, useContext, useState, useEffect } from 'react';
import { Driver } from '../types';
import { mockDrivers } from '../data/mockData';

interface DriverContextType {
  drivers: Driver[];
  filteredDrivers: Driver[];
  selectedDriver: Driver | null;
  isLoading: boolean;
  error: string | null;
  fetchDrivers: () => Promise<void>;
  getDriverById: (id: string) => Promise<Driver | null>;
  setSelectedDriver: (driver: Driver | null) => void;
  addDriver: (driver: Omit<Driver, 'id' | 'createdAt'>) => Promise<Driver>;
  updateDriver: (id: string, driver: Partial<Driver>) => Promise<Driver>;
  deleteDriver: (id: string) => Promise<void>;
  filterDrivers: (query: string) => void;
  toggleDriverAvailability: (id: string) => Promise<Driver>;
}

const DriverContext = createContext<DriverContextType | undefined>(undefined);

export const useDrivers = () => {
  const context = useContext(DriverContext);
  if (!context) {
    throw new Error('useDrivers must be used within a DriverProvider');
  }
  return context;
};

export const DriverProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [drivers, setDrivers] = useState<Driver[]>([]);
  const [filteredDrivers, setFilteredDrivers] = useState<Driver[]>([]);
  const [selectedDriver, setSelectedDriver] = useState<Driver | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchDrivers = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Simulate API call with mock data
      // In a real app, this would be an API request
      setDrivers(mockDrivers);
      setFilteredDrivers(mockDrivers);
    } catch (err) {
      setError('Failed to fetch drivers');
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  const getDriverById = async (id: string): Promise<Driver | null> => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Find driver by ID
      const driver = mockDrivers.find(driver => driver.id === id) || null;
      setSelectedDriver(driver);
      return driver;
    } catch (err) {
      setError('Failed to fetch driver');
      console.error(err);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  const addDriver = async (driverData: Omit<Driver, 'id' | 'createdAt'>): Promise<Driver> => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Create new driver
      const newDriver: Driver = {
        ...driverData,
        id: `driver-${Date.now()}`,
        createdAt: new Date().toISOString(),
      };
      
      setDrivers(prevDrivers => [...prevDrivers, newDriver]);
      setFilteredDrivers(prevDrivers => [...prevDrivers, newDriver]);
      return newDriver;
    } catch (err) {
      setError('Failed to add driver');
      console.error(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const updateDriver = async (id: string, driverData: Partial<Driver>): Promise<Driver> => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Find and update driver
      const driverIndex = drivers.findIndex(driver => driver.id === id);
      
      if (driverIndex === -1) {
        throw new Error('Driver not found');
      }
      
      const updatedDriver = {
        ...drivers[driverIndex],
        ...driverData,
      };
      
      const updatedDrivers = [...drivers];
      updatedDrivers[driverIndex] = updatedDriver;
      
      setDrivers(updatedDrivers);
      setFilteredDrivers(updatedDrivers);
      
      if (selectedDriver?.id === id) {
        setSelectedDriver(updatedDriver);
      }
      
      return updatedDriver;
    } catch (err) {
      setError('Failed to update driver');
      console.error(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const deleteDriver = async (id: string): Promise<void> => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Filter out the driver with the given ID
      const updatedDrivers = drivers.filter(driver => driver.id !== id);
      
      setDrivers(updatedDrivers);
      setFilteredDrivers(updatedDrivers);
      
      if (selectedDriver?.id === id) {
        setSelectedDriver(null);
      }
    } catch (err) {
      setError('Failed to delete driver');
      console.error(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const filterDrivers = (query: string) => {
    if (!query.trim()) {
      setFilteredDrivers(drivers);
      return;
    }
    
    const lowerQuery = query.toLowerCase();
    const filtered = drivers.filter(driver => 
      driver.name.toLowerCase().includes(lowerQuery) ||
      driver.location.toLowerCase().includes(lowerQuery) ||
      driver.specialties.some(specialty => specialty.toLowerCase().includes(lowerQuery))
    );
    
    setFilteredDrivers(filtered);
  };

  const toggleDriverAvailability = async (id: string): Promise<Driver> => {
    const driver = drivers.find(d => d.id === id);
    if (!driver) {
      throw new Error('Driver not found');
    }
    
    return updateDriver(id, { isAvailable: !driver.isAvailable });
  };

  useEffect(() => {
    fetchDrivers();
  }, []);

  const value = {
    drivers,
    filteredDrivers,
    selectedDriver,
    isLoading,
    error,
    fetchDrivers,
    getDriverById,
    setSelectedDriver,
    addDriver,
    updateDriver,
    deleteDriver,
    filterDrivers,
    toggleDriverAvailability,
  };

  return <DriverContext.Provider value={value}>{children}</DriverContext.Provider>;
};

export default DriverContext;
