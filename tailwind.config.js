/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#F0F4F0',
          100: '#DCE8DC',
          200: '#C2D5C2',
          300: '#A3BEA3',
          400: '#7FA07F',
          500: '#5D805D',
          600: '#4C684C',
          700: '#3D533D',
          800: '#2F402F',
          900: '#1F2E1F',
          950: '#0F1A0F',
        },
        secondary: {
          50: '#F0F4F0',
          100: '#DCE8DC',
          200: '#C2D5C2',
          300: '#A3BEA3',
          400: '#7FA07F',
          500: '#5D805D',
          600: '#4C684C',
          700: '#3D533D',
          800: '#2F402F',
          900: '#1F2E1F',
          950: '#0F1A0F',
        },
        accent: {
          50: '#F0F4F0',
          100: '#DCE8DC',
          200: '#C2D5C2',
          300: '#A3BEA3',
          400: '#7FA07F',
          500: '#5D805D',
          600: '#4C684C',
          700: '#3D533D',
          800: '#2F402F',
          900: '#1F2E1F',
          950: '#0F1A0F',
        },
        success: {
          50: '#F0FDF4',
          100: '#DCFCE7',
          200: '#BBF7D0',
          300: '#86EFAC',
          400: '#4ADE80',
          500: '#22C55E',
          600: '#16A34A',
          700: '#15803D',
          800: '#166534',
          900: '#14532D',
          950: '#052E16',
        },
        warning: {
          50: '#FEFCE8',
          100: '#FEF9C3',
          200: '#FEF08A',
          300: '#FDE047',
          400: '#FACC15',
          500: '#EAB308',
          600: '#CA8A04',
          700: '#A16207',
          800: '#854D0E',
          900: '#713F12',
          950: '#422006',
        },
        error: {
          50: '#FEF2F2',
          100: '#FEE2E2',
          200: '#FECACA',
          300: '#FCA5A5',
          400: '#F87171',
          500: '#EF4444',
          600: '#DC2626',
          700: '#B91C1C',
          800: '#991B1B',
          900: '#7F1D1D',
          950: '#450A0A',
        },
      },
      spacing: {
        '4.5': '1.125rem',
      },
      animation: {
        'fade-in': 'fadeIn 0.3s ease-in-out',
        'slide-in': 'slideIn 0.3s ease-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideIn: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
      }
    },
  },
  plugins: [],
};