import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Car, Upload, X, Plus, MapPin, DollarSign, Info, AlertCircle } from 'lucide-react';
import MainLayout from '../../components/layout/MainLayout';
import Button from '../../components/ui/Button';
import { useCars } from '../../context/CarContext';
import { useAuth } from '../../context/AuthContext';

const AddCarPage: React.FC = () => {
  const navigate = useNavigate();
  const { addCar } = useCars();
  const { user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    make: '',
    model: '',
    year: new Date().getFullYear(),
    description: '',
    location: '',
    pricePerHour: 0,
    availabilityNotes: '',
  });

  // Features state
  const [features, setFeatures] = useState<string[]>([]);
  const [newFeature, setNewFeature] = useState('');

  // Images state
  const [images, setImages] = useState<string[]>([]);
  const [imageUrl, setImageUrl] = useState('');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: name === 'year' || name === 'pricePerHour' ? Number(value) : value,
    });
  };

  const handleAddFeature = () => {
    if (newFeature.trim() && !features.includes(newFeature.trim())) {
      setFeatures([...features, newFeature.trim()]);
      setNewFeature('');
    }
  };

  const handleRemoveFeature = (feature: string) => {
    setFeatures(features.filter(f => f !== feature));
  };

  const handleAddImage = () => {
    if (imageUrl.trim() && !images.includes(imageUrl.trim())) {
      setImages([...images, imageUrl.trim()]);
      setImageUrl('');
    }
  };

  const handleRemoveImage = (image: string) => {
    setImages(images.filter(img => img !== image));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (!formData.make || !formData.model || !formData.description || !formData.location || formData.pricePerHour <= 0) {
      setError('Please fill in all required fields');
      return;
    }

    if (images.length === 0) {
      setError('Please add at least one image');
      return;
    }

    if (features.length === 0) {
      setError('Please add at least one feature');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      if (!user) {
        throw new Error('You must be logged in to add a car');
      }

      await addCar({
        ownerId: user.id,
        make: formData.make,
        model: formData.model,
        year: formData.year,
        images,
        description: formData.description,
        features,
        location: formData.location,
        pricePerHour: formData.pricePerHour,
        availabilityNotes: formData.availabilityNotes,
        isActive: true,
      });

      setSuccess(true);

      // Redirect after a short delay
      setTimeout(() => {
        navigate('/owner/dashboard');
      }, 2000);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add car');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-3xl mx-auto">
          <div className="flex items-center mb-6">
            <button
              onClick={() => navigate('/owner/dashboard')}
              className="text-primary-600 hover:text-primary-800 mr-4"
            >
              ← Back to Dashboard
            </button>
            <h1 className="text-3xl font-bold text-gray-900">Add New Car</h1>
          </div>

          {success ? (
            <div className="bg-success-100 border border-success-200 text-success-800 px-4 py-3 rounded mb-6">
              <p className="font-medium">Car added successfully!</p>
              <p>Redirecting to dashboard...</p>
            </div>
          ) : (
            <form onSubmit={handleSubmit}>
              {error && (
                <div className="bg-error-100 border border-error-200 text-error-800 px-4 py-3 rounded mb-6">
                  {error}
                </div>
              )}

              <div className="bg-white rounded-lg shadow-md overflow-hidden mb-6">
                <div className="p-6">
                  <h2 className="text-xl font-semibold mb-4">Car Details</h2>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                      <label htmlFor="make" className="block text-sm font-medium text-gray-700 mb-1">
                        Make <span className="text-error-600">*</span>
                      </label>
                      <input
                        type="text"
                        id="make"
                        name="make"
                        value={formData.make}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                        required
                      />
                    </div>

                    <div>
                      <label htmlFor="model" className="block text-sm font-medium text-gray-700 mb-1">
                        Model <span className="text-error-600">*</span>
                      </label>
                      <input
                        type="text"
                        id="model"
                        name="model"
                        value={formData.model}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                        required
                      />
                    </div>
                  </div>

                  <div className="mb-6">
                    <label htmlFor="year" className="block text-sm font-medium text-gray-700 mb-1">
                      Year <span className="text-error-600">*</span>
                    </label>
                    <select
                      id="year"
                      name="year"
                      value={formData.year}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                      required
                    >
                      {Array.from({ length: 30 }, (_, i) => new Date().getFullYear() - i).map(year => (
                        <option key={year} value={year}>{year}</option>
                      ))}
                    </select>
                  </div>

                  <div className="mb-6">
                    <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                      Description <span className="text-error-600">*</span>
                    </label>
                    <textarea
                      id="description"
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      rows={4}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                      required
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                      <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-1">
                        Location <span className="text-error-600">*</span>
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <MapPin size={18} className="text-gray-400" />
                        </div>
                        <input
                          type="text"
                          id="location"
                          name="location"
                          value={formData.location}
                          onChange={handleInputChange}
                          className="w-full pl-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                          placeholder="City, Country"
                          required
                        />
                      </div>
                    </div>

                    <div>
                      <label htmlFor="pricePerHour" className="block text-sm font-medium text-gray-700 mb-1">
                        Price per Hour (RWF) <span className="text-error-600">*</span>
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <DollarSign size={18} className="text-gray-400" />
                        </div>
                        <input
                          type="number"
                          id="pricePerHour"
                          name="pricePerHour"
                          value={formData.pricePerHour}
                          onChange={handleInputChange}
                          min="0"
                          step="1000"
                          className="w-full pl-10 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                          required
                        />
                      </div>
                    </div>
                  </div>

                  <div className="mb-6">
                    <label htmlFor="availabilityNotes" className="block text-sm font-medium text-gray-700 mb-1">
                      Availability Notes
                    </label>
                    <textarea
                      id="availabilityNotes"
                      name="availabilityNotes"
                      value={formData.availabilityNotes}
                      onChange={handleInputChange}
                      rows={2}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="e.g., Available on weekends only"
                    />
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-md overflow-hidden mb-6">
                <div className="p-6">
                  <h2 className="text-xl font-semibold mb-4">Features</h2>

                  <div className="flex mb-4">
                    <input
                      type="text"
                      value={newFeature}
                      onChange={(e) => setNewFeature(e.target.value)}
                      className="flex-grow px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="Add a feature (e.g., Air Conditioning)"
                    />
                    <Button
                      type="button"
                      onClick={handleAddFeature}
                      className="rounded-l-none"
                    >
                      <Plus size={18} className="mr-1" /> Add
                    </Button>
                  </div>

                  {features.length === 0 ? (
                    <div className="text-gray-500 italic mb-4">
                      No features added yet. Please add at least one feature.
                    </div>
                  ) : (
                    <div className="flex flex-wrap gap-2 mb-4">
                      {features.map((feature, index) => (
                        <div
                          key={index}
                          className="flex items-center bg-gray-100 px-3 py-1 rounded-full"
                        >
                          <span className="text-sm">{feature}</span>
                          <button
                            type="button"
                            onClick={() => handleRemoveFeature(feature)}
                            className="ml-2 text-gray-500 hover:text-error-600"
                          >
                            <X size={14} />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-md overflow-hidden mb-6">
                <div className="p-6">
                  <h2 className="text-xl font-semibold mb-4">Images</h2>

                  <div className="flex mb-4">
                    <input
                      type="url"
                      value={imageUrl}
                      onChange={(e) => setImageUrl(e.target.value)}
                      className="flex-grow px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="Enter image URL"
                    />
                    <Button
                      type="button"
                      onClick={handleAddImage}
                      className="rounded-l-none"
                    >
                      <Upload size={18} className="mr-1" /> Add
                    </Button>
                  </div>

                  {images.length === 0 ? (
                    <div className="text-gray-500 italic mb-4">
                      No images added yet. Please add at least one image.
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-4">
                      {images.map((image, index) => (
                        <div key={index} className="relative group">
                          <img
                            src={image}
                            alt={`Car image ${index + 1}`}
                            className="w-full h-40 object-cover rounded-md"
                            onError={(e) => {
                              (e.target as HTMLImageElement).src = 'https://via.placeholder.com/300x200?text=Image+Error';
                            }}
                          />
                          <button
                            type="button"
                            onClick={() => handleRemoveImage(image)}
                            className="absolute top-2 right-2 bg-white rounded-full p-1 shadow-md text-gray-700 hover:text-error-600"
                          >
                            <X size={16} />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}

                  <div className="text-sm text-gray-500 flex items-start">
                    <Info size={16} className="mr-1 mt-0.5 flex-shrink-0" />
                    <span>
                      For now, please use image URLs. In a production app, you would be able to upload images directly.
                    </span>
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => navigate('/owner/dashboard')}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Adding Car...' : 'Add Car'}
                </Button>
              </div>
            </form>
          )}
        </div>
      </div>
    </MainLayout>
  );
};

export default AddCarPage;
