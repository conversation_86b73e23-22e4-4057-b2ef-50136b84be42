import React, { useState } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface ImageCarouselProps {
  images: string[];
  alt: string;
}

const ImageCarousel: React.FC<ImageCarouselProps> = ({ images, alt }) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const goToPrevious = () => {
    const isFirstSlide = currentIndex === 0;
    const newIndex = isFirstSlide ? images.length - 1 : currentIndex - 1;
    setCurrentIndex(newIndex);
  };

  const goToNext = () => {
    const isLastSlide = currentIndex === images.length - 1;
    const newIndex = isLastSlide ? 0 : currentIndex + 1;
    setCurrentIndex(newIndex);
  };

  const goToSlide = (slideIndex: number) => {
    setCurrentIndex(slideIndex);
  };

  if (images.length === 0) {
    return (
      <div className="w-full h-96 bg-gray-200 flex items-center justify-center rounded-lg">
        <span className="text-gray-500">No images available</span>
      </div>
    );
  }

  if (images.length === 1) {
    return (
      <div className="w-full rounded-lg overflow-hidden">
        <img
          src={images[0]}
          alt={alt}
          className="w-full h-96 object-cover"
          onError={(e) => {
            (e.target as HTMLImageElement).src = 'https://via.placeholder.com/800x400?text=Image+Not+Available';
          }}
        />
      </div>
    );
  }

  return (
    <div className="relative w-full h-96">
      <div
        className="absolute top-0 left-0 right-0 bottom-0 w-full h-full rounded-lg overflow-hidden"
      >
        <img
          src={images[currentIndex]}
          alt={`${alt} - Image ${currentIndex + 1}`}
          className="absolute w-full h-full object-cover transition-opacity duration-300"
          onError={(e) => {
            (e.target as HTMLImageElement).src = 'https://via.placeholder.com/800x400?text=Image+Not+Available';
          }}
        />
      </div>

      {/* Left Arrow */}
      <div className="absolute top-1/2 left-4 -translate-y-1/2">
        <button
          onClick={goToPrevious}
          className="p-2 rounded-full bg-white bg-opacity-70 hover:bg-opacity-100 text-gray-800 transition-all"
          aria-label="Previous image"
        >
          <ChevronLeft size={24} />
        </button>
      </div>

      {/* Right Arrow */}
      <div className="absolute top-1/2 right-4 -translate-y-1/2">
        <button
          onClick={goToNext}
          className="p-2 rounded-full bg-white bg-opacity-70 hover:bg-opacity-100 text-gray-800 transition-all"
          aria-label="Next image"
        >
          <ChevronRight size={24} />
        </button>
      </div>

      {/* Dots */}
      <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex space-x-2">
        {images.map((_, slideIndex) => (
          <button
            key={slideIndex}
            onClick={() => goToSlide(slideIndex)}
            className={`h-2.5 w-2.5 rounded-full transition-all ${
              slideIndex === currentIndex ? 'bg-white w-5' : 'bg-white bg-opacity-50'
            }`}
            aria-label={`Go to image ${slideIndex + 1}`}
          />
        ))}
      </div>
    </div>
  );
};

export default ImageCarousel;